FROM apache/tika:latest

# Set environment variables for better performance and file handling
ENV JAVA_OPTS="-Xmx2g -Xms512m -Djava.awt.headless=true -Djava.security.egd=file:/dev/./urandom"
ENV TIKA_SERVER_ENDPOINT="/"
ENV TIKA_CONFIG_PATH="/tmp/tika-config.xml"

# Create necessary directories
USER root
RUN mkdir -p /tmp/tika-temp && \
    chmod 777 /tmp/tika-temp && \
    mkdir -p /tmp/tika-logs && \
    chmod 777 /tmp/tika-logs

# Copy custom configuration
COPY tika-config.xml /tmp/tika-config.xml

# Set proper permissions
RUN chown -R tika:tika /tmp/tika-temp /tmp/tika-logs /tmp/tika-config.xml

# Switch back to tika user
USER tika

# Expose the port
EXPOSE 9998

# Health check
HEALTHCHECK --interval=30s --timeout=10s --retries=3 --start-period=40s \
  CMD curl -f http://localhost:9998/ || exit 1

# Start Tika server with custom configuration
CMD ["java", "-jar", "/tika-server-standard.jar", "--config=/tmp/tika-config.xml", "--host=0.0.0.0", "--port=9998"]
