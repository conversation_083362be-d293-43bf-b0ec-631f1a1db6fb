<?xml version="1.0" encoding="UTF-8"?>
<properties>
  <service-loader initializableProblemHandler="ignore"/>
  
  <!-- Server Configuration -->
  <server>
    <!-- Enable CORS for cross-origin requests -->
    <enableCors>true</enableCors>
    
    <!-- File upload settings -->
    <maxFileSize>104857600</maxFileSize> <!-- 100MB in bytes -->
    <maxRequestSize>104857600</maxRequestSize> <!-- 100MB in bytes -->
    
    <!-- Timeout settings -->
    <taskTimeoutMillis>300000</taskTimeoutMillis> <!-- 5 minutes -->
    <taskPulseMillis>1000</taskPulseMillis>
    
    <!-- Enable unsafe features for better document processing -->
    <enableUnsecureFeatures>false</enableUnsecureFeatures>
    
    <!-- Temporary directory for file processing -->
    <tempFilePrefix>tika-temp-</tempFilePrefix>
    <tempDir>/tmp/tika-temp</tempDir>
    
    <!-- Return stack traces in error responses for debugging -->
    <returnStackTrace>false</returnStackTrace>
    
    <!-- Enable specific endpoints -->
    <endpoints>
      <endpoint>all</endpoint>
    </endpoints>
    
    <!-- Logging configuration -->
    <logLevel>INFO</logLevel>
  </server>
  
  <!-- Parser Configuration -->
  <parsers>
    <!-- Enable auto-detection of file types -->
    <parser class="org.apache.tika.parser.AutoDetectParser">
      <mime>application/pdf</mime>
      <mime>application/msword</mime>
      <mime>application/vnd.openxmlformats-officedocument.wordprocessingml.document</mime>
      <mime>application/vnd.ms-excel</mime>
      <mime>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</mime>
      <mime>application/vnd.ms-powerpoint</mime>
      <mime>application/vnd.openxmlformats-officedocument.presentationml.presentation</mime>
      <mime>text/plain</mime>
      <mime>text/html</mime>
      <mime>text/xml</mime>
      <mime>application/rtf</mime>
      <mime>application/epub+zip</mime>
    </parser>
  </parsers>
  
  <!-- Detector Configuration -->
  <detectors>
    <detector class="org.apache.tika.detect.DefaultDetector"/>
  </detectors>
  
  <!-- Metadata Configuration -->
  <metadataFilters>
    <!-- Remove potentially sensitive metadata -->
    <metadataFilter class="org.apache.tika.metadata.filter.CompositeMetadataFilter">
      <metadataFilter class="org.apache.tika.metadata.filter.FieldNameMappingFilter">
        <excludeUnmapped>false</excludeUnmapped>
      </metadataFilter>
    </metadataFilter>
  </metadataFilters>
</properties>
