#!/bin/bash

# Deploy Tika Server Fix Script
# This script will rebuild and restart the Tika server with proper file upload configuration

set -e

echo "🔧 Deploying Tika Server Fix for File Upload Issues..."

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install docker-compose first."
    exit 1
fi

# Stop the current Tika service
echo "🛑 Stopping current Tika service..."
docker-compose stop tika || true

# Remove the old Tika container and image
echo "🗑️  Removing old Tika container and image..."
docker-compose rm -f tika || true
docker rmi $(docker images | grep pilardin.*tika | awk '{print $3}') 2>/dev/null || true

# Create necessary volumes
echo "📁 Creating necessary volumes..."
docker volume create pilardin_tika_tmp 2>/dev/null || true
docker volume create pilardin_tika_logs 2>/dev/null || true

# Build the new Tika image
echo "🔨 Building new Tika image with custom configuration..."
docker-compose build tika

# Start the services
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check Tika server health
echo "🏥 Checking Tika server health..."
for i in {1..30}; do
    if curl -f http://localhost:9998/ >/dev/null 2>&1; then
        echo "✅ Tika server is healthy and ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Tika server failed to start properly"
        echo "📋 Checking logs..."
        docker-compose logs tika
        exit 1
    fi
    echo "⏳ Waiting for Tika server... ($i/30)"
    sleep 2
done

# Test file upload capability
echo "🧪 Testing file upload capability..."
if curl -X PUT -H "Content-Type: text/plain" -d "Test content" http://localhost:9998/tika/text >/dev/null 2>&1; then
    echo "✅ File upload test successful!"
else
    echo "⚠️  File upload test failed, but server is running. Check logs if issues persist."
fi

# Show service status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🎉 Tika Server Fix Deployment Complete!"
echo ""
echo "📝 What was fixed:"
echo "   • Increased file upload limits to 100MB"
echo "   • Added proper temporary directory handling"
echo "   • Configured Java heap settings for better performance"
echo "   • Added CORS support for cross-origin requests"
echo "   • Extended timeout settings for large file processing"
echo ""
echo "🔗 Tika server is now available at: http://localhost:9998/"
echo "🔗 Your Pilardin RAG application should now be able to upload files properly!"
echo ""
echo "💡 If you still experience issues:"
echo "   1. Check the logs: docker-compose logs tika"
echo "   2. Verify the Tika server URL in your admin settings"
echo "   3. Ensure your file sizes are within the 100MB limit"
