# Tika Server File Upload Fix

## Problem
The Tika server in your production environment at http://51.75.182.123:9998/ was not properly configured to handle file uploads, even though it was accessible and in the same Docker network as your other services.

## Root Cause
The default Apache Tika Docker image has limited file upload capabilities and lacks proper configuration for:
- File upload size limits
- Temporary directory handling
- Java heap settings for large files
- CORS support
- Timeout configurations

## Solution
This fix provides a comprehensive Tika server configuration that includes:

### 1. Custom Tika Configuration (`tika-config.xml`)
- **File Upload Limits**: Increased to 100MB for both file size and request size
- **Timeout Settings**: Extended to 5 minutes for large file processing
- **Temporary Directory**: Proper temp file handling with dedicated directory
- **CORS Support**: Enabled for cross-origin requests
- **Parser Configuration**: Optimized for common document formats

### 2. Custom Dockerfile (`Dockerfile.tika`)
- **Java Heap Settings**: Configured with 2GB max heap and 512MB initial heap
- **Proper Permissions**: Ensures tika user has access to temp directories
- **Health Checks**: Built-in health monitoring
- **Security**: Runs as non-root tika user

### 3. Updated Docker Compose Configuration
- **Custom Build**: Uses our custom Dockerfile instead of default image
- **Volume Mounts**: Dedicated volumes for temp files and logs
- **Environment Variables**: Proper Java and Tika configuration
- **Network Integration**: Maintains compatibility with existing services

## Files Modified/Added

### New Files:
- `tika-config.xml` - Custom Tika server configuration
- `Dockerfile.tika` - Custom Tika Docker image
- `deploy-tika-fix.sh` - Deployment script
- `TIKA_FIX_README.md` - This documentation

### Modified Files:
- `docker-compose.yaml` - Updated Tika service configuration

## Deployment Instructions

### Option 1: Automated Deployment (Recommended)
```bash
# Run the deployment script
./deploy-tika-fix.sh
```

### Option 2: Manual Deployment
```bash
# Stop current Tika service
docker-compose stop tika

# Remove old container and image
docker-compose rm -f tika
docker rmi $(docker images | grep pilardin.*tika | awk '{print $3}') 2>/dev/null || true

# Build new image
docker-compose build tika

# Start services
docker-compose up -d

# Check health
curl -f http://localhost:9998/
```

## Configuration Details

### File Upload Limits
- **Maximum File Size**: 100MB (104,857,600 bytes)
- **Maximum Request Size**: 100MB (104,857,600 bytes)
- **Task Timeout**: 5 minutes (300,000 milliseconds)

### Java Settings
- **Max Heap**: 2GB (-Xmx2g)
- **Initial Heap**: 512MB (-Xms512m)
- **Headless Mode**: Enabled for server environment
- **Security**: Uses secure random number generation

### Supported File Types
- PDF documents
- Microsoft Office documents (DOC, DOCX, XLS, XLSX, PPT, PPTX)
- Text files (TXT, HTML, XML)
- Rich Text Format (RTF)
- EPUB books
- And many more through auto-detection

## Verification

After deployment, verify the fix works by:

1. **Health Check**: `curl -f http://localhost:9998/`
2. **Upload Test**: Try uploading a file through your Pilardin RAG interface
3. **Log Check**: `docker-compose logs tika` to see any issues

## Troubleshooting

### If file uploads still fail:
1. Check Tika server logs: `docker-compose logs tika`
2. Verify file size is under 100MB limit
3. Ensure proper network connectivity between services
4. Check backend configuration for Tika server URL

### Common Issues:
- **Out of Memory**: Increase Java heap size in docker-compose.yaml
- **Permission Errors**: Ensure volumes have proper permissions
- **Network Issues**: Verify all services are in the same Docker network

## Production Considerations

For production deployment:
1. Consider increasing file size limits if needed
2. Monitor Java heap usage and adjust accordingly
3. Set up proper log rotation for Tika logs
4. Consider adding authentication if Tika is exposed externally
5. Regular health monitoring and alerting

## Rollback Instructions

If you need to rollback to the original configuration:
```bash
# Restore original docker-compose.yaml
git checkout docker-compose.yaml

# Remove custom files
rm tika-config.xml Dockerfile.tika

# Rebuild with original image
docker-compose stop tika
docker-compose rm -f tika
docker-compose up -d tika
```
